{"$schema": "http://json-schema.org/draft-07/schema#", "title": "HybridPipe Message", "description": "Schema for HybridPipe messages serialized in JSON format", "type": "object", "required": ["header", "payload"], "properties": {"header": {"type": "object", "required": ["version", "format", "timestamp", "messageId"], "properties": {"version": {"type": "integer", "description": "Message format version", "minimum": 1}, "format": {"type": "string", "description": "Serialization format used for the payload", "enum": ["json", "protobuf", "msgpack", "cbor", "bson"]}, "compression": {"type": "boolean", "description": "Whether the payload is compressed", "default": false}, "timestamp": {"type": "integer", "description": "Unix timestamp (milliseconds) when the message was created"}, "messageId": {"type": "string", "description": "Unique identifier for the message"}, "correlationId": {"type": "string", "description": "Identifier for correlating related messages"}, "replyTo": {"type": "string", "description": "Pipe name to send replies to"}, "ttl": {"type": "integer", "description": "Time-to-live in milliseconds", "minimum": 0}, "priority": {"type": "integer", "description": "Message priority (higher values indicate higher priority)", "minimum": 0, "maximum": 9, "default": 4}, "contentType": {"type": "string", "description": "MIME type of the payload content"}}}, "payload": {"description": "The message content", "oneOf": [{"type": "object"}, {"type": "array"}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "null"}]}, "metadata": {"type": "object", "description": "Additional metadata for the message", "additionalProperties": true}}}