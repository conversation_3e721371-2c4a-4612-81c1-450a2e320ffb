// NATS protocol implementation for HybridPipe
//
// This module provides an implementation of the HybridPipe interface for NATS messaging system.

#[cfg(not(test))]
use async_nats::{Client, ConnectOptions, Subscriber};

// We'll use the real client types for now
#[cfg(test)]
use async_nats::{Client, ConnectOptions, Subscriber};

use async_trait::async_trait;
use futures::StreamExt;
use log::{debug, error, info};
use std::any::Any;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;

use crate::core::config::{get_config, NATSConfig};
use crate::core::registry::register_router_factory;
use crate::core::{context, BrokerType, Error, HybridPipe, Process, Result};

/// Packet implements the HybridPipe interface for NATS messaging system.
pub struct Packet {
    /// Client is the NATS client
    client: Mutex<Option<Client>>,
    /// Subscribers maps pipe names to their subscribers
    subscribers: RwLock<HashMap<String, Subscriber>>,
    /// Server is the NATS server address with port
    server: String,
    /// Subscriber tasks maps pipe names to their subscriber task handles
    subscriber_tasks: RwLock<HashMap<String, JoinHandle<()>>>,
    /// Config holds the NATS configuration
    config: NATSConfig,
    /// Connected flag indicates if the packet is connected
    connected: Mutex<bool>,
}

impl Packet {
    /// Create a new NATS packet.
    pub fn new() -> Self {
        // Get the NATS configuration
        let config = match get_config() {
            Ok(config) => config.nats.clone(),
            Err(_) => NATSConfig {
                n_servers: "localhost".to_string(),
                n_lport: 4222,
                n_mport: 8222,
                n_cport: 6222,
                nats_cert_file: String::new(),
                nats_key_file: String::new(),
                nats_ca_file: String::new(),
                n_allow_reconnect: true,
                n_max_attempt: 10,
                n_reconnect_wait: 5,
                n_timeout: 2,
            },
        };

        // Create the server address
        let server = format!("{}:{}", config.n_servers, config.n_lport);

        Self {
            client: Mutex::new(None),
            subscribers: RwLock::new(HashMap::new()),
            server,
            subscriber_tasks: RwLock::new(HashMap::new()),
            config,
            connected: Mutex::new(false),
        }
    }
}

#[async_trait]
impl HybridPipe for Packet {
    async fn connect(&self) -> Result<()> {
        // Check if already connected
        {
            let connected = self.connected.lock().unwrap();
            if *connected {
                return Err(Error::AlreadyConnected);
            }
        }

        // Create NATS connection options
        let mut options = ConnectOptions::new();

        // Set connection timeout
        options = options.connection_timeout(Duration::from_secs(self.config.n_timeout as u64));

        // Set reconnect options
        if self.config.n_allow_reconnect {
            options = options.retry_on_initial_connect();
            // max_reconnects is not available in the current version of async-nats
            let reconnect_wait = self.config.n_reconnect_wait;
            options = options.reconnect_delay_callback(move |attempts| {
                Duration::from_secs((attempts as u64).saturating_mul(reconnect_wait as u64))
            });
        }

        // Add TLS configuration if certificate files are provided
        if !self.config.nats_cert_file.is_empty()
            && !self.config.nats_key_file.is_empty()
            && !self.config.nats_ca_file.is_empty()
        {
            // Load certificates
            let _cert = tokio::fs::read(&self.config.nats_cert_file)
                .await
                .map_err(|e| {
                    Error::ConnectionError(format!("Failed to read NATS certificate file: {}", e))
                })?;

            let _key = tokio::fs::read(&self.config.nats_key_file)
                .await
                .map_err(|e| {
                    Error::ConnectionError(format!("Failed to read NATS key file: {}", e))
                })?;

            let _ca = tokio::fs::read(&self.config.nats_ca_file)
                .await
                .map_err(|e| {
                    Error::ConnectionError(format!("Failed to read NATS CA file: {}", e))
                })?;

            // Create TLS configuration
            // The API has changed in the current version of async-nats
            // We'll use a simplified approach
            options = options.require_tls(true);

            // Note: For proper TLS with client certificates and custom CA,
            // we would need to use the tls_client_config method with a properly
            // configured rustls::ClientConfig, but that's beyond the scope of this fix

            // The tls_config method has been renamed to tls_client_config in the current version
            // But we're using require_tls(true) instead for simplicity
        }

        // Connect to NATS server
        let client = async_nats::connect_with_options(&self.server, options)
            .await
            .map_err(|e| {
                Error::ConnectionError(format!("Failed to connect to NATS server: {}", e))
            })?;

        // Store the client
        let mut client_guard = self.client.lock().unwrap();
        *client_guard = Some(client);
        drop(client_guard);

        // Set connected flag
        {
            let mut connected = self.connected.lock().unwrap();
            *connected = true;
        }

        info!("Connected to NATS server at {}", self.server);
        Ok(())
    }

    async fn disconnect(&self) -> Result<()> {
        // Check if connected and set flag to false
        {
            let mut connected = self.connected.lock().unwrap();
            if !*connected {
                return Err(Error::NotConnected);
            }
            *connected = false;
        }

        // Cancel all subscriber tasks
        let mut subscriber_tasks = self.subscriber_tasks.write().await;
        for (pipe, task) in subscriber_tasks.drain() {
            task.abort();
            debug!("Cancelled subscriber task for pipe: {}", pipe);
        }

        // Clear the subscribers
        let mut subscribers = self.subscribers.write().await;
        subscribers.clear();

        // Close the client
        {
            let mut client_guard = self.client.lock().unwrap();
            if let Some(client) = client_guard.take() {
                drop(client); // This will close the connection
            }
        }

        info!("Disconnected from NATS server at {}", self.server);
        Ok(())
    }

    async fn dispatch(&self, pipe: &str, data: Box<dyn Any + Send + Sync>) -> Result<()> {
        // Check if connected
        {
            let connected = self.connected.lock().unwrap();
            if !*connected {
                return Err(Error::NotConnected);
            }
        }

        // Get the client
        let client = {
            let client_guard = self.client.lock().unwrap();
            match client_guard.as_ref() {
                Some(client) => client.clone(),
                None => return Err(Error::NotConnected),
            }
        };

        // Convert the data to a JSON string
        let json_string = serde_json::to_string(&format!("{:?}", data))
            .map_err(|e| Error::SerializationError(format!("Failed to encode message: {}", e)))?;

        // Convert the JSON string to bytes
        let bytes = json_string.into_bytes();

        // Publish the message
        // Create a copy of the pipe string to avoid borrowing issues
        let pipe_owned = pipe.to_string();
        client
            .publish(pipe_owned, bytes.into())
            .await
            .map_err(|e| Error::DispatchError(format!("Failed to publish message: {}", e)))?;

        Ok(())
    }

    async fn dispatch_with_context(
        &self,
        ctx: &context::Context,
        pipe: &str,
        data: Box<dyn Any + Send + Sync>,
    ) -> Result<()> {
        // Use the context to run the dispatch with a timeout
        ctx.run(self.dispatch(pipe, data)).await
    }

    async fn subscribe(&self, pipe: &str, callback: Process) -> Result<()> {
        // Check if connected
        {
            let connected = self.connected.lock().unwrap();
            if !*connected {
                return Err(Error::NotConnected);
            }
        }

        // Check if already subscribed
        let subscribers = self.subscribers.read().await;
        if subscribers.contains_key(pipe) {
            return Err(Error::AlreadySubscribed(pipe.to_string()));
        }
        drop(subscribers);

        // Get the client
        let client = {
            let client_guard = self.client.lock().unwrap();
            match client_guard.as_ref() {
                Some(client) => client.clone(),
                None => return Err(Error::NotConnected),
            }
        };

        // Subscribe to the subject
        // Create a copy of the pipe string to avoid borrowing issues
        let pipe_owned = pipe.to_string();
        let subscriber = client.subscribe(pipe_owned).await.map_err(|e| {
            Error::SubscriptionError(format!("Failed to subscribe to subject: {}", e))
        })?;

        // In the current version of async-nats, Subscriber doesn't implement Clone
        // We'll need to use a different approach
        let mut subscribers = self.subscribers.write().await;
        subscribers.insert(pipe.to_string(), subscriber);

        // Get a new subscriber for the same subject
        let client = {
            let client_guard = self.client.lock().unwrap();
            match client_guard.as_ref() {
                Some(client) => client.clone(),
                None => return Err(Error::NotConnected),
            }
        };

        // Create another copy of the pipe string
        let pipe_owned2 = pipe.to_string();
        let mut subscriber = client.subscribe(pipe_owned2).await.map_err(|e| {
            Error::SubscriptionError(format!("Failed to subscribe to subject: {}", e))
        })?;
        drop(subscribers);

        // Create a task to process messages
        let pipe_owned = pipe.to_string();
        let callback_owned = callback;
        let task = tokio::spawn(async move {
            info!("Started NATS subscriber for pipe: {}", pipe_owned);

            while let Some(msg) = subscriber.next().await {
                if let Err(e) = callback_owned(msg.payload.to_vec()) {
                    error!("Error processing message from pipe {}: {}", pipe_owned, e);
                }
            }
        });

        // Store the task
        let mut subscriber_tasks = self.subscriber_tasks.write().await;
        subscriber_tasks.insert(pipe.to_string(), task);

        info!("Subscribed to NATS subject: {}", pipe);
        Ok(())
    }

    async fn subscribe_with_context(
        &self,
        ctx: &context::Context,
        pipe: &str,
        callback: Process,
    ) -> Result<()> {
        // Use the context to run the subscribe with a timeout
        ctx.run(self.subscribe(pipe, callback)).await
    }

    async fn remove(&self, pipe: &str) -> Result<()> {
        self.unsubscribe(pipe).await
    }

    async fn unsubscribe(&self, pipe: &str) -> Result<()> {
        // Check if connected
        {
            let connected = self.connected.lock().unwrap();
            if !*connected {
                return Err(Error::NotConnected);
            }
        }

        // Check if subscribed
        let subscribers = self.subscribers.read().await;
        if !subscribers.contains_key(pipe) {
            return Err(Error::NotSubscribed(pipe.to_string()));
        }
        drop(subscribers);

        // Cancel the subscriber task
        let mut subscriber_tasks = self.subscriber_tasks.write().await;
        if let Some(task) = subscriber_tasks.remove(pipe) {
            task.abort();
        }

        // Remove the subscriber
        let mut subscribers = self.subscribers.write().await;
        subscribers.remove(pipe);

        info!("Unsubscribed from NATS subject: {}", pipe);
        Ok(())
    }

    async fn close(&self) -> Result<()> {
        self.disconnect().await
    }

    fn is_connected(&self) -> bool {
        let connected = self.connected.lock().unwrap();
        *connected
    }
}

/// Register the NATS protocol with the HybridPipe system.
pub fn register() {
    register_router_factory(BrokerType::NATS, Box::new(|| Arc::new(Packet::new())));
}

#[cfg(test)]
mod tests;
