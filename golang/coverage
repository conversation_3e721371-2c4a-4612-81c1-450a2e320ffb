mode: set
hybridpipe.io/protocols/mock/mock.go:11.13,13.63 1 1
hybridpipe.io/protocols/mock/mock.go:13.63,15.3 1 1
hybridpipe.io/protocols/mock/mock.go:27.34,33.2 1 1
hybridpipe.io/protocols/mock/mock.go:36.38,42.2 4 1
hybridpipe.io/protocols/mock/mock.go:45.41,51.2 4 1
hybridpipe.io/protocols/mock/mock.go:54.60,58.18 3 1
hybridpipe.io/protocols/mock/mock.go:58.18,60.3 1 1
hybridpipe.io/protocols/mock/mock.go:63.2,65.36 3 1
hybridpipe.io/protocols/mock/mock.go:65.36,68.17 3 1
hybridpipe.io/protocols/mock/mock.go:68.17,70.4 1 0
hybridpipe.io/protocols/mock/mock.go:74.2,74.44 1 1
hybridpipe.io/protocols/mock/mock.go:74.44,76.3 1 1
hybridpipe.io/protocols/mock/mock.go:77.2,80.51 2 1
hybridpipe.io/protocols/mock/mock.go:80.51,81.28 1 1
hybridpipe.io/protocols/mock/mock.go:81.28,83.4 1 1
hybridpipe.io/protocols/mock/mock.go:86.2,86.12 1 1
hybridpipe.io/protocols/mock/mock.go:90.92,91.9 1 1
hybridpipe.io/protocols/mock/mock.go:92.20,93.19 1 1
hybridpipe.io/protocols/mock/mock.go:94.10,95.32 1 1
hybridpipe.io/protocols/mock/mock.go:100.62,103.18 2 1
hybridpipe.io/protocols/mock/mock.go:103.18,106.3 2 1
hybridpipe.io/protocols/mock/mock.go:107.2,110.15 2 1
hybridpipe.io/protocols/mock/mock.go:110.15,112.3 1 1
hybridpipe.io/protocols/mock/mock.go:115.2,115.37 1 1
hybridpipe.io/protocols/mock/mock.go:115.37,118.3 2 0
hybridpipe.io/protocols/mock/mock.go:121.2,121.35 1 1
hybridpipe.io/protocols/mock/mock.go:125.74,127.21 1 1
hybridpipe.io/protocols/mock/mock.go:127.21,129.3 1 1
hybridpipe.io/protocols/mock/mock.go:131.2,134.18 3 1
hybridpipe.io/protocols/mock/mock.go:134.18,136.3 1 1
hybridpipe.io/protocols/mock/mock.go:139.2,139.49 1 1
hybridpipe.io/protocols/mock/mock.go:139.49,141.3 1 1
hybridpipe.io/protocols/mock/mock.go:142.2,145.50 2 1
hybridpipe.io/protocols/mock/mock.go:145.50,146.32 1 0
hybridpipe.io/protocols/mock/mock.go:146.32,148.4 1 0
hybridpipe.io/protocols/mock/mock.go:151.2,151.12 1 1
hybridpipe.io/protocols/mock/mock.go:155.106,156.9 1 1
hybridpipe.io/protocols/mock/mock.go:157.20,158.19 1 1
hybridpipe.io/protocols/mock/mock.go:159.10,160.37 1 1
hybridpipe.io/protocols/mock/mock.go:165.48,167.2 1 1
hybridpipe.io/protocols/mock/mock.go:170.53,174.18 3 1
hybridpipe.io/protocols/mock/mock.go:174.18,176.3 1 1
hybridpipe.io/protocols/mock/mock.go:178.2,179.12 2 1
hybridpipe.io/protocols/mock/mock.go:183.36,185.2 1 1
hybridpipe.io/protocols/mock/mock.go:188.41,193.2 3 1
