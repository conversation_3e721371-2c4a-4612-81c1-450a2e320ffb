# This is Hybrid Framework Configuration File. This file would or should be placed under directory
# "/opt/<ClientApp>/config/". This file contains all communication middleware related configurations defined.

[NATS]
NServers           = "localhost"
NLPort             = 4222
NMPort             = 8222
NCPort             = 6222
NATSCertFile       = "/home/<USER>/go/src/hybridpipe/platforms/tls/nats-client.pem"
NATSKeyFile        = "/home/<USER>/go/src/hybridpipe/platforms/tls/nats-client.key"
NATSCAFile         = "/home/<USER>/go/src/hybridpipe/platforms/tls/nats-server-ca.pem"
NAllow_Reconnect   = true
NMax_Attempt       = 10
NReconnect_Wait    = 5
NTimeout           = 2

[KAFKA]
KServers   = "Anand-iMac"
KLPort     = 9093
KTimeout   = 10
KAFKACertFile       = "/home/<USER>/go/src/hybridpipe/platforms/tls/mytestclient.cert.pem"
KAFKAKeyFile        = "/home/<USER>/go/src/hybridpipe/platforms/tls/mytestclient.key.pem"
KAFKACAFile         = "/home/<USER>/go/src/hybridpipe/platforms/tls/rootca.cert.pem"

[RABBITMQ]
RServerPort    = "amqp://guest:guest@localhost:5672/"

[AMQP1]
AMQPServer = "amqp://0.0.0.0:5672/"
AMQPPort   = 5672

[QPID]
QpidServer = "localhost"
QpidPort = 5672
QpidUsername = "guest"
QpidPassword = "guest"
QpidCertFile = "/path/to/qpid-client.pem"
QpidKeyFile = "/path/to/qpid-client.key"
QpidCAFile = "/path/to/qpid-ca.pem"
QpidTimeout = 10

[MQTT]
MQTTBroker = "tcp://localhost:1883"
MQTTClientID = "hybridpipe-client"
MQTTUsername = "user"
MQTTPassword = "password"
MQTTCleanSession = true
MQTTQoS = 1
MQTTRetained = false
MQTTCertFile = "/path/to/mqtt-client.pem"
MQTTKeyFile = "/path/to/mqtt-client.key"
MQTTCAFile = "/path/to/mqtt-ca.pem"
MQTTConnectTimeout = 10

[NSQ]
NSQLookupdAddresses = "localhost:4161"
NSQDAddress = "localhost"
NSQDPort = 4150
NSQClientID = "hybridpipe-client"
NSQAuthSecret = ""
NSQMaxInFlight = 100
NSQMaxAttempts = 5
NSQRequeueDelay = 10
NSQConnectTimeout = 10

[TCP]
TCPHost = "localhost"
TCPPort = 9000
TCPMode = "client"
TCPBufferSize = 4096
TCPKeepAlive = true
TCPKeepAlivePeriod = 30
TCPConnectTimeout = 10
TCPTLSEnabled = false
TCPCertFile = "/path/to/tcp-client.pem"
TCPKeyFile = "/path/to/tcp-client.key"
TCPCAFile = "/path/to/tcp-ca.pem"

[REDIS]
RedisAddress = "localhost"
RedisPort = 6379
RedisPassword = ""
RedisDB = 0
RedisUsername = ""
RedisPoolSize = 10
RedisMinIdleConns = 2
RedisConnectTimeout = 10
RedisReadTimeout = 5
RedisWriteTimeout = 5
RedisTLSEnabled = false
RedisCertFile = "/path/to/redis-client.pem"
RedisKeyFile = "/path/to/redis-client.key"
RedisCAFile = "/path/to/redis-ca.pem"

# Note: ZeroMQ configuration has been removed from the system

[NETCHAN]
NetChanServer = "localhost"
NetChanPort = 8080
NetChanBufferSize = 10
NetChanTimeout = 5000

[GENERAL]
DBLocation     = "/Users/<USER>/go/config"
