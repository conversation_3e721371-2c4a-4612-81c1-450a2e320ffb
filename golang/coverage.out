mode: set
hybridpipe.io/protocols/kafka/kafka.go:37.44,44.2 1 1
hybridpipe.io/protocols/kafka/kafka.go:47.77,48.53 1 0
hybridpipe.io/protocols/kafka/kafka.go:48.53,50.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:53.2,54.16 2 0
hybridpipe.io/protocols/kafka/kafka.go:54.16,56.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:59.2,60.16 2 0
hybridpipe.io/protocols/kafka/kafka.go:60.16,62.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:64.2,65.44 2 0
hybridpipe.io/protocols/kafka/kafka.go:65.44,67.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:70.2,76.23 2 0
hybridpipe.io/protocols/kafka/kafka.go:80.35,82.22 1 0
hybridpipe.io/protocols/kafka/kafka.go:82.22,84.17 2 0
hybridpipe.io/protocols/kafka/kafka.go:84.17,86.4 1 0
hybridpipe.io/protocols/kafka/kafka.go:87.3,88.27 2 0
hybridpipe.io/protocols/kafka/kafka.go:92.2,100.16 3 0
hybridpipe.io/protocols/kafka/kafka.go:100.16,102.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:105.2,106.16 2 0
hybridpipe.io/protocols/kafka/kafka.go:106.16,108.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:111.2,122.23 4 0
hybridpipe.io/protocols/kafka/kafka.go:122.23,124.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:125.2,125.23 1 0
hybridpipe.io/protocols/kafka/kafka.go:125.23,127.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:128.2,128.28 1 0
hybridpipe.io/protocols/kafka/kafka.go:128.28,130.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:132.2,133.12 2 0
hybridpipe.io/protocols/kafka/kafka.go:137.57,140.26 2 1
hybridpipe.io/protocols/kafka/kafka.go:140.26,143.3 2 1
hybridpipe.io/protocols/kafka/kafka.go:144.2,149.13 4 0
hybridpipe.io/protocols/kafka/kafka.go:149.13,163.3 2 0
hybridpipe.io/protocols/kafka/kafka.go:164.2,168.16 3 0
hybridpipe.io/protocols/kafka/kafka.go:168.16,170.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:173.2,183.59 4 0
hybridpipe.io/protocols/kafka/kafka.go:183.59,185.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:187.2,187.12 1 0
hybridpipe.io/protocols/kafka/kafka.go:191.89,194.26 2 1
hybridpipe.io/protocols/kafka/kafka.go:194.26,197.3 2 1
hybridpipe.io/protocols/kafka/kafka.go:198.2,203.13 4 0
hybridpipe.io/protocols/kafka/kafka.go:203.13,215.3 2 0
hybridpipe.io/protocols/kafka/kafka.go:216.2,220.16 3 0
hybridpipe.io/protocols/kafka/kafka.go:220.16,222.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:225.2,232.59 2 0
hybridpipe.io/protocols/kafka/kafka.go:232.59,234.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:236.2,236.12 1 0
hybridpipe.io/protocols/kafka/kafka.go:240.59,242.37 1 1
hybridpipe.io/protocols/kafka/kafka.go:242.37,244.53 2 0
hybridpipe.io/protocols/kafka/kafka.go:244.53,246.4 1 0
hybridpipe.io/protocols/kafka/kafka.go:247.3,248.13 2 0
hybridpipe.io/protocols/kafka/kafka.go:251.2,251.36 1 1
hybridpipe.io/protocols/kafka/kafka.go:255.71,258.26 2 1
hybridpipe.io/protocols/kafka/kafka.go:258.26,261.3 2 1
hybridpipe.io/protocols/kafka/kafka.go:262.2,269.53 4 0
hybridpipe.io/protocols/kafka/kafka.go:269.53,272.3 2 0
hybridpipe.io/protocols/kafka/kafka.go:275.2,275.48 1 0
hybridpipe.io/protocols/kafka/kafka.go:275.48,278.3 2 0
hybridpipe.io/protocols/kafka/kafka.go:281.2,302.12 7 0
hybridpipe.io/protocols/kafka/kafka.go:306.103,309.26 2 1
hybridpipe.io/protocols/kafka/kafka.go:309.26,312.3 2 1
hybridpipe.io/protocols/kafka/kafka.go:313.2,320.53 4 0
hybridpipe.io/protocols/kafka/kafka.go:320.53,323.3 2 0
hybridpipe.io/protocols/kafka/kafka.go:326.2,326.48 1 0
hybridpipe.io/protocols/kafka/kafka.go:326.48,329.3 2 0
hybridpipe.io/protocols/kafka/kafka.go:332.2,353.12 7 0
hybridpipe.io/protocols/kafka/kafka.go:357.111,358.15 1 0
hybridpipe.io/protocols/kafka/kafka.go:358.15,359.31 1 0
hybridpipe.io/protocols/kafka/kafka.go:359.31,361.4 1 0
hybridpipe.io/protocols/kafka/kafka.go:364.2,364.6 1 0
hybridpipe.io/protocols/kafka/kafka.go:364.6,365.10 1 0
hybridpipe.io/protocols/kafka/kafka.go:366.21,368.10 2 0
hybridpipe.io/protocols/kafka/kafka.go:369.11,376.24 4 0
hybridpipe.io/protocols/kafka/kafka.go:376.24,378.5 1 0
hybridpipe.io/protocols/kafka/kafka.go:381.4,381.39 1 0
hybridpipe.io/protocols/kafka/kafka.go:381.39,382.13 1 0
hybridpipe.io/protocols/kafka/kafka.go:386.4,386.18 1 0
hybridpipe.io/protocols/kafka/kafka.go:386.18,389.32 2 0
hybridpipe.io/protocols/kafka/kafka.go:389.32,391.6 1 0
hybridpipe.io/protocols/kafka/kafka.go:392.5,392.13 1 0
hybridpipe.io/protocols/kafka/kafka.go:396.4,396.50 1 0
hybridpipe.io/protocols/kafka/kafka.go:396.50,398.5 1 0
hybridpipe.io/protocols/kafka/kafka.go:404.45,406.2 1 1
hybridpipe.io/protocols/kafka/kafka.go:409.50,412.26 2 1
hybridpipe.io/protocols/kafka/kafka.go:412.26,415.3 2 1
hybridpipe.io/protocols/kafka/kafka.go:416.2,424.13 5 0
hybridpipe.io/protocols/kafka/kafka.go:424.13,426.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:429.2,429.53 1 0
hybridpipe.io/protocols/kafka/kafka.go:429.53,432.3 2 0
hybridpipe.io/protocols/kafka/kafka.go:435.2,435.39 1 0
hybridpipe.io/protocols/kafka/kafka.go:435.39,437.3 1 0
hybridpipe.io/protocols/kafka/kafka.go:438.2,441.12 3 0
hybridpipe.io/protocols/kafka/kafka.go:445.38,448.26 2 1
hybridpipe.io/protocols/kafka/kafka.go:448.26,451.3 2 1
hybridpipe.io/protocols/kafka/kafka.go:452.2,459.44 4 0
hybridpipe.io/protocols/kafka/kafka.go:459.44,463.3 3 0
hybridpipe.io/protocols/kafka/kafka.go:466.2,466.39 1 0
hybridpipe.io/protocols/kafka/kafka.go:466.39,467.40 1 0
hybridpipe.io/protocols/kafka/kafka.go:467.40,469.4 1 0
hybridpipe.io/protocols/kafka/kafka.go:471.2,474.39 2 0
hybridpipe.io/protocols/kafka/kafka.go:474.39,475.40 1 0
hybridpipe.io/protocols/kafka/kafka.go:475.40,477.4 1 0
hybridpipe.io/protocols/kafka/kafka.go:479.2,485.12 4 0
hybridpipe.io/protocols/kafka/kafka.go:489.33,491.2 1 1
hybridpipe.io/protocols/kafka/kafka.go:494.38,498.2 3 1
hybridpipe.io/protocols/kafka/register.go:10.17,12.2 1 1
hybridpipe.io/protocols/kafka/register.go:15.13,17.2 1 1
