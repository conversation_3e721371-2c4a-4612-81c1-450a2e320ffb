
## NATS Configuration File
## Author: <PERSON> S <<EMAIL>>

# Client Listening Port Configuration
# Port to listen for client connections from gnatsd end
port: 4222         # DEFAULT = 4222
# net: localhost   # DEFAULT = 0.0.0.0 - ALL (Listening Node / Interface)

# HTTP Monitoring Port Configuration
monitor_port: 8222  # DEFAULT = 8222

# Client Authentication Information
# NATS provided utility used for Password creation
# "$ ./util/mkpasswd -p WhatTheHell"
# authorization {
#        user:     anands
#	password: this4now
#	timeout:  1
# }

tls {
        cert_file: "/home/<USER>/go/src/hybridpipe/platforms/tls/nats-server.pem"
        key_file:  "/home/<USER>/go/src/hybridpipe/platforms/tls/nats-server.key"
        ca_file:   "/home/<USER>/go/src/hybridpipe/platforms/tls/nats-server-ca.pem"
        verify:    true
        timeout:   1
}

# Cluster Routes Definition
# cluster {
#	listen: localhost:4445  # Host/Port for inbound route connections

	# Authorization for Route Connection Requests
	# authorization {
	#	user:     app_router
	#	password: $2a$11$xH8dkGrty1cBNtZjhPeWJewu/YPbSU.rXJWmS6SFilOBXzmZoMk9m
	#	timeout:  0.5
	# }

	# If specified, All in cluster would verify both connecting endpoints and
	# responses optional certificate authority verifying connected routes Required
	# when we have self-signed CA, etc.

	# Instead of below Config, users can pass following command line options
	# as well for enabling and using TLS
	# 	--tls                        Enable TLS, do not verify clients (default: false)
	# 	--tlscert FILE               Server certificate file
	# 	--tlskey FILE                Private key for server certificate
	# 	--tlsverify                  Enable TLS, verify client certificates
	# 	--tlscacert FILE             Client certificate CA for verification
  	# tls {
    	#	cert_file: "./configs/certs/srva-cert.pem"  # Route Certificate
    	#	key_file:  "./configs/certs/srva-key.pem"   # private Key
    	#	ca_file:   "./configs/certs/ca.pem"
	# }

	# Apart from these servers, we can add other NATS servers as well into cluster
	# provided if they are passing the right credentials and details as
	# routes = [
	#	nats-route://app1:pwd1@127.0.0.1:4448
	#	nats-route://app2:pwd2@127.0.0.1:4449
	# ]
# }

# Logging and PID related Configuration / definition
# debug:    false
# trace:    true
# logtime:  false

# log_file: "/app-nats-mq.log"
# pid_file: "/app-nats-mq.pid"

# Maximum Client Connections & Max Payload Size for Each Message
max_connections:  100
max_control_line: 512    # Max Protocol Control Line
max_payload:      65536

# TLS Encryption Configuration
# tls {
#	cert_file:  "./configs/certs/server-cert.pem"
#	key_file:   "./configs/certs/server-key.pem"
#	timeout:    2
# }

# TIMEOUT Configurations
write_deadline: "2s"	# Duration of time which Server can block its socket for the Client
			# Exceeding this time, Client will be deemed as "Slow Consumer" by NATS Server
			# This parameter will make sure we try to achieve "Atleast Once" Message Delivery SLA.
