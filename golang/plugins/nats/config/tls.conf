# ============================================================================
# NATS Server TLS Configuration for HybridPipe.io
# ============================================================================
# This configuration file extends the base configuration with TLS settings
# for secure communication.
#
# Author: HybridPipe.io
# Date: April 21, 2025
# Version: 2.0
# ============================================================================

# Include the base configuration
include "nats.conf"

# Override server name for TLS instance
server_name: hybridpipe-nats-tls

# TLS configuration
tls {
  cert_file: "/nats/config/certs/server.pem"
  key_file: "/nats/config/certs/server.key"
  ca_file: "/nats/config/certs/ca.pem"
  verify: true
  timeout: 2
  
  # Modern cipher suites for enhanced security
  cipher_suites: [
    "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
    "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
    "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305",
    "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
  ]
  
  # Curve preferences
  curve_preferences: [
    "CurveP521",
    "CurveP384"
  ]
}
