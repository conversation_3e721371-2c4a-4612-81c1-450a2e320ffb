# ============================================================================
# NATS Server Configuration for HybridPipe.io
# ============================================================================
# This is the main configuration file for the NATS server used with HybridPipe.io.
# It provides a balance of performance, security, and flexibility.
#
# Author: HybridPipe.io
# Date: April 21, 2025
# Version: 2.0
# ============================================================================

# Server identification
server_name: hybridpipe-nats

# Client listening port
port: 4222
http_port: 8222

# Logging configuration
debug: false
trace: false
logtime: true
log_format: json

# Security settings
# -----------------
# Uncomment and configure these sections as needed

# Basic authentication
# authorization {
#   user: hybridpipe
#   password: CHANGE_ME_IN_PRODUCTION
#   timeout: 2
# }

# TLS configuration
# tls {
#   cert_file: "/nats/config/certs/server.pem"
#   key_file: "/nats/config/certs/server.key"
#   ca_file: "/nats/config/certs/ca.pem"
#   verify: true
#   timeout: 2
#   cipher_suites: [
#     "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
#     "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
#   ]
#   curve_preferences: [
#     "CurveP521",
#     "CurveP384"
#   ]
# }

# Performance tuning
# -----------------
# These settings are optimized for HybridPipe.io workloads

# Connection limits
max_connections: 10000
max_control_line: 4096
max_payload: 1048576  # 1MB
max_pending: 67108864  # 64MB

# Protocol settings
ping_interval: 120
ping_max: 3
write_deadline: "5s"

# System limits
# Uncomment to set specific limits
# max_subscriptions: 1000
# max_msgs: 10000
# max_bytes: 104857600  # 100MB
# max_age: "1h"

# Jetstream configuration
# Uncomment to enable JetStream (persistent messaging)
# jetstream {
#   store_dir: "/nats/data"
#   max_memory: 1073741824  # 1GB
#   max_file: 10737418240   # 10GB
# }

# Clustering configuration
# Uncomment to enable clustering
# cluster {
#   name: "hybridpipe_cluster"
#   listen: 0.0.0.0:6222
#   
#   # Cluster authorization
#   authorization {
#     user: cluster_user
#     password: CHANGE_ME_IN_PRODUCTION
#     timeout: 2
#   }
#   
#   # Routes to other cluster members
#   routes = [
#     nats://cluster_user:CHANGE_ME_IN_PRODUCTION@nats-1:6222
#     nats://cluster_user:CHANGE_ME_IN_PRODUCTION@nats-2:6222
#   ]
# }

# Leafnode configuration
# Uncomment to enable leafnodes
# leafnodes {
#   listen: 0.0.0.0:7777
# }

# Websocket configuration
# Uncomment to enable websocket support
# websocket {
#   listen: 0.0.0.0:7422
#   no_tls: true
# }
