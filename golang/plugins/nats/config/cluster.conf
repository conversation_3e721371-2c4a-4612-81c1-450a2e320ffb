# ============================================================================
# NATS Server Cluster Configuration for HybridPipe.io
# ============================================================================
# This configuration file extends the base configuration with cluster settings
# for high availability and scalability.
#
# Author: HybridPipe.io
# Date: April 21, 2025
# Version: 2.0
# ============================================================================

# Include the base configuration
include "nats.conf"

# Override server name for cluster instance
server_name: hybridpipe-nats-cluster-$SERVER_ID

# Clustering configuration
cluster {
  name: "hybridpipe_cluster"
  listen: 0.0.0.0:6222
  
  # Cluster authorization
  authorization {
    user: cluster_user
    password: $CLUSTER_PASSWORD
    timeout: 2
  }
  
  # Routes to other cluster members
  # These can be overridden with environment variables
  routes = [
    nats://cluster_user:$CLUSTER_PASSWORD@nats-1:6222
    nats://cluster_user:$CLUSTER_PASSWORD@nats-2:6222
    nats://cluster_user:$CLUSTER_PASSWORD@nats-3:6222
  ]
  
  # TLS configuration for cluster communication
  tls {
    cert_file: "/nats/config/certs/cluster.pem"
    key_file: "/nats/config/certs/cluster.key"
    ca_file: "/nats/config/certs/ca.pem"
    verify: true
    timeout: 2
  }
}

# JetStream configuration for persistent messaging
jetstream {
  store_dir: "/nats/data"
  max_memory: 1073741824  # 1GB
  max_file: 10737418240   # 10GB
}
