Step 1 — Creating a User for Kafka

	sudo useradd kafka -m
	sudo passwd kafka
	sudo adduser kafka sudo
	su -l kafka

Step 2 — Downloading and Extracting the Kafka Binaries

	curl "https://www.apache.org/dist/kafka/2.2.0/kafka_2.11-2.2.0.tgz" -o ~/Downloads/HYBRID_KAFKA.tgz
	mkdir ~/APP_KAFKA && cd ~/APP_KAFKA
	tar -xvzf ~/Downloads/HYBRID_KAFKA.tgz --strip 1

Step 3 — Configuring the Kafka Server

	KAFKA Config Files in  ($KAFKAROOT/config) folder needs to be updated on requirement.
	server.properties, zookeeper.properties

Step 4 — Creating Systemd Unit Files and Starting the Kafka Server. Done as part of this Repo

Final Step - Test and Use

	Use systemctl commands for controlling these services
	sudo journalctl -u kafka .... etc
	Use $KAFKAROOT/bin scripts to control, configure and run KAFKA
