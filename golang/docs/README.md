# Documentation

This directory contains documentation for the HybridPipe system.

## Purpose

The `docs` directory is intended for comprehensive documentation of the HybridPipe system, including:

- System architecture
- Protocol implementations
- Configuration options
- Usage examples
- API reference
- Troubleshooting guides

## Structure

- `architecture/` - System architecture documentation
- `protocols/` - Protocol-specific documentation
- `examples/` - Usage examples
- `api/` - API reference documentation
- `config/` - Configuration documentation

## Contributing

When adding new features or protocols to the HybridPipe system, please update the relevant documentation in this directory.

Last updated: April 19, 2025
