name: Test

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: 1.18
    
    - name: Check out code
      uses: actions/checkout@v2
    
    - name: Install dependencies
      run: |
        go mod download
    
    - name: Run tests (short mode)
      run: make test-short
    
    - name: Check test coverage
      run: make check-coverage
    
    - name: Run linter
      run: |
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.42.1
        make lint
    
  test-with-docker:
    name: Test with Docker
    runs-on: ubuntu-latest
    
    steps:
    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: 1.18
    
    - name: Check out code
      uses: actions/checkout@v2
    
    - name: Install dependencies
      run: |
        go mod download
    
    - name: Run tests with Docker
      run: make test-docker-coverage
    
    - name: Upload coverage report
      uses: actions/upload-artifact@v2
      with:
        name: coverage-report
        path: coverage/coverage.html
